import os
import argparse
import numpy as np
from PIL import Image
import onnxsim
import onnx
import nncase
import shutil

def parse_model_input_output(model_file, input_shape):
    onnx_model = onnx.load(model_file)
    input_all = [node.name for node in onnx_model.graph.input]
    input_initializer = [node.name for node in onnx_model.graph.initializer]
    input_names = list(set(input_all) - set(input_initializer))
    input_tensors = [
        node for node in onnx_model.graph.input if node.name in input_names]

    # input
    inputs = []
    for _, e in enumerate(input_tensors):
        onnx_type = e.type.tensor_type
        input_dict = {}
        input_dict['name'] = e.name
        # 处理弃用警告
        input_dict['dtype'] = onnx.helper.tensor_dtype_to_np_dtype(onnx_type.elem_type)
        input_dict['shape'] = [(i.dim_value if i.dim_value != 0 else d) for i, d in zip(
            onnx_type.shape.dim, input_shape)]
        inputs.append(input_dict)

    return onnx_model, inputs


def onnx_simplify(model_file, dump_dir, input_shape):
    onnx_model, inputs = parse_model_input_output(model_file, input_shape)
    onnx_model = onnx.shape_inference.infer_shapes(onnx_model)
    input_shapes = {}
    for input in inputs:
        input_shapes[input['name']] = input['shape']

    # 处理弃用警告
    onnx_model, check = onnxsim.simplify(onnx_model, overwrite_input_shapes=input_shapes)
    assert check, "Simplified ONNX model could not be validated"

    model_file = os.path.join(dump_dir, 'simplified.onnx')
    onnx.save_model(onnx_model, model_file)
    return model_file


def read_model_file(model_file):
    with open(model_file, 'rb') as f:
        model_content = f.read()
    return model_content


def generate_data(shape, batch, calib_dir):
    if calib_dir is None:
        print("错误：未指定校准数据集目录。")
        return []
    img_paths = [os.path.join(calib_dir, p) for p in os.listdir(calib_dir)]
    if len(img_paths) < batch:
        print(f"警告：校准图像数量不足，将样本数量调整为 {len(img_paths)}。")
        batch = len(img_paths)

    data = []
    for i in range(batch):
        img_data = Image.open(img_paths[i]).convert('RGB')
        img_data = img_data.resize((shape[3], shape[2]), Image.BILINEAR)
        img_data = np.asarray(img_data, dtype=np.uint8)
        img_data = np.transpose(img_data, (2, 0, 1))
        data.append([img_data[np.newaxis, ...]])
    return np.array(data)


def main():
    parser = argparse.ArgumentParser(prog="nncase")
    parser.add_argument("--target", default="k230", type=str, help='target to run,k230/cpu')
    parser.add_argument("--model", type=str, default="./runs/train/exp/weights/best.onnx", help='model file')
    parser.add_argument("--dataset", type=str,default="./mydata/YOLO_data/images/test", help='calibration_dataset')
    parser.add_argument("--input_width", type=int, default=640, help='input_width')
    parser.add_argument("--input_height", type=int, default=640, help='input_height')
    parser.add_argument("--ptq_option", type=int, default=0, help='ptq_option:0,1,2,3,4')

    args = parser.parse_args()

    print(f"开始转换模型: {args.model}")
    print(f"输入尺寸: {args.input_width}x{args.input_height}")
    print(f"校准数据集: {args.dataset}")

    # 模型的输入shape，维度要跟input_layout一致
    input_shape = [1, 3, args.input_height, args.input_width]
    print(f"输入形状: {input_shape}")

    dump_dir = 'tmp'
    if not os.path.exists(dump_dir):
        os.makedirs(dump_dir)

    # onnx simplify
    print("开始简化ONNX模型...")
    model_file = onnx_simplify(args.model, dump_dir, input_shape)
    print(f"简化完成，模型文件: {model_file}")

    # compile_options
    print("配置编译选项...")
    compile_options = nncase.CompileOptions()
    compile_options.target = args.target
    # preprocess
    # 是否采用模型做预处理
    compile_options.preprocess = True
    compile_options.swapRB = False
    # 输入图像的shape
    compile_options.input_shape = input_shape
    # 模型输入格式‘uint8’或者‘float32’
    compile_options.input_type = 'uint8'

    # 如果输入是‘uint8’格式，输入反量化之后的范围
    compile_options.input_range = [0, 1]

    # 预处理的mean/std值，每个channel一个,这里注意，分类任务和检测任务Norm的参数不同
    compile_options.mean = [0, 0, 0]  # gray有图像，bgr没有图像
    compile_options.std = [1, 1, 1]

    # 设置输入的layout，onnx默认‘NCHW’即可
    compile_options.input_layout = "NCHW"

    compile_options.quant_type = 'uint8'

    # compiler
    print("创建编译器...")
    compiler = nncase.Compiler(compile_options)

    # import
    print("导入ONNX模型...")
    model_content = read_model_file(model_file)
    import_options = nncase.ImportOptions()
    compiler.import_onnx(model_content, import_options)
    print("模型导入完成")

    # ptq_options
    print("配置量化选项...")
    ptq_options = nncase.PTQTensorOptions()
    ptq_options.samples_count = 20

    if args.ptq_option == 0:
        pass
    elif args.ptq_option == 1:
        ptq_options.calibrate_method = 'NoClip'
        ptq_options.w_quant_type = 'int16'
    elif args.ptq_option == 2:
        ptq_options.calibrate_method = 'NoClip'
        ptq_options.quant_type = 'int16'

    print("生成校准数据...")
    ptq_options.set_tensor_data(generate_data(input_shape, ptq_options.samples_count, args.dataset))
    compiler.use_ptq(ptq_options)
    print("校准数据设置完成")

    # compile
    print("开始编译模型（这可能需要几分钟）...")
    compiler.compile()
    print("模型编译完成")

    # kmodel
    print("生成kmodel文件...")
    kmodel = compiler.gencode_tobytes()
    base, _ = os.path.splitext(args.model)
    kmodel_name = base + ".kmodel"
    with open(kmodel_name, 'wb') as f:
        f.write(kmodel)

    print(f"✅ kmodel转换成功！")
    print(f"输出文件: {kmodel_name}")
    print(f"文件大小: {len(kmodel) / 1024 / 1024:.2f} MB")

    # 清理临时文件
    if os.path.exists("./tmp"):
        shutil.rmtree("./tmp")
    if os.path.exists("./gmodel_dump_dir"):
        shutil.rmtree("./gmodel_dump_dir")
    print("临时文件清理完成")


if __name__ == '__main__':
    main()