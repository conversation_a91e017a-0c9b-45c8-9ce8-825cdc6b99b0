# K230 垃圾分类检测配置文件
import sys
import os

# K230 环境配置
class K230Config:
    def __init__(self):
        self.model_path = "runs/train/exp/weights/best.pt"  # 模型路径
        self.confidence_threshold = 0.5  # 置信度阈值
        self.device = "cpu"  # K230 使用 CPU
        self.input_size = (640, 640)  # 输入尺寸
        
    def setup_environment(self):
        """设置 K230 环境"""
        try:
            # 尝试导入 ultralytics
            from ultralytics import YOLO
            print("成功导入 ultralytics.YOLO")
            return True
        except ImportError as e:
            print(f"导入错误: {e}")
            print("请确保在 K230 环境中安装了 ultralytics 库")
            return False
    
    def load_model(self):
        """加载模型"""
        try:
            from ultralytics import YOLO
            model = YOLO(self.model_path)
            print(f"成功加载模型: {self.model_path}")
            return model
        except Exception as e:
            print(f"模型加载失败: {e}")
            return None

# 使用示例
if __name__ == "__main__":
    config = K230Config()
    if config.setup_environment():
        model = config.load_model()
        if model:
            print("K230 环境配置成功")
        else:
            print("模型加载失败")
    else:
        print("环境配置失败")
